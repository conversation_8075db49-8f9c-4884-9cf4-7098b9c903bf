import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

export class VirusCheckExecutor extends BaseStepExecutor {
	constructor() {
		super("Virus check");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Starting virus check for step: ${step.name}`);

		try {
			this.validate(step);
			await this.sleep(1500);

			return this.createSuccessResult("Virus check completed - no threats detected");
		} catch (error: any) {
			return this.createErrorResult(`Virus check failed: ${error.message}`, error.message);
		}
	}

	validate(step: any): boolean {
		super.validate(step);
		if (step.operation_type !== "Virus check") {
			throw new Error(`Invalid operation type for virus check: ${step.operation_type}`);
		}
		return true;
	}
}
