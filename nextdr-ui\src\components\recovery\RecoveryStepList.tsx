import React from "react";
import { RecoveryStep } from "@/lib/types";
import { useModalStore } from "@/lib/store/useStore";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { motion } from "framer-motion";
import { Info, Pencil, GripVertical, Trash, AlertCircle, Plus } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { Button } from "@/components/ui/button";
import { useDeleteRecoveryStep } from "@/lib/api/hooks/recoveryPlans";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import IaCRecoveryStep from "@/components/recovery/IaCRecoveryStep";
import VMRestoreStep from "@/components/recovery/VMRestoreStep";
import ExecutionProgress from "@/components/recovery/ExecutionProgress";
import { useExecutionProgress } from "@/hooks/useExecutionProgress";

interface RecoveryStepListProps {
	steps: RecoveryStep[];
	planId?: string;
	datacenterId?: string;
	onEditStep: (step: RecoveryStep) => void;
	onReorderSteps: (steps: RecoveryStep[]) => void;
	onDeleteStep?: (step: RecoveryStep) => void;
}

const RecoveryStepList: React.FC<RecoveryStepListProps> = ({
	steps,
	planId,
	datacenterId,
	onEditStep,
	onReorderSteps,
	onDeleteStep,
}) => {
	const { onOpen } = useModalStore();
	const deleteStepMutation = useDeleteRecoveryStep();

	// Get execution progress to determine if we should show step management or execution view
	const { progress, isExecuting } = useExecutionProgress(planId);

	// Determine if we should show execution interface
	const showExecutionInterface = planId && datacenterId && (progress || isExecuting);
	const showStepManagement = !showExecutionInterface;

	const handleAddStep = () => {
		if (!planId) {
			toast.error("Cannot add step: Missing recovery plan ID");
			return;
		}
		onOpen("recoveryStep", { planId });
	};

	const handleEditStep = (step: RecoveryStep) => {
		onEditStep(step);
	};

	const handleDeleteStep = (step: RecoveryStep) => {
		onOpen("confirmDialog", {
			title: "Delete Recovery Step",
			message: `Are you sure you want to delete the step "${step.name}"? This action cannot be undone.`,
			confirmLabel: "Delete",
			cancelLabel: "Cancel",
			variant: "destructive",
			onConfirm: async () => {
				try {
					await deleteStepMutation.mutateAsync(step.id);
					toast.success(`Step "${step.name}" deleted successfully`);

					if (onDeleteStep) {
						onDeleteStep(step);
					}
				} catch (error) {
					console.error("Error deleting recovery step:", error);
					toast.error("Failed to delete recovery step");
				}
			},
		});
	};

	const handleInfoClick = (step: RecoveryStep) => {
		onOpen("stepInfo", {
			step,
			title: "Step Information",
			content: (
				<div className="space-y-4">
					<div>
						<h4 className="font-medium mb-2">Step Details</h4>
						<div className="space-y-2 text-sm">
							<p>
								<span className="text-muted-foreground">Name:</span> {step.name}
							</p>
							<p>
								<span className="text-muted-foreground">Type:</span> {step.operation_type}
							</p>
							<p>
								<span className="text-muted-foreground">Order:</span> {step.step_order}
							</p>
						</div>
					</div>
					{step.operation_type === "Restore virtual machine" && (
						<div>
							<h4 className="font-medium mb-2">VM Configuration</h4>
							<div className="space-y-2 text-sm">
								{Object.entries(step.configuration).map(([key, value]) => (
									<p key={key}>
										<span className="text-muted-foreground">{key}:</span> {String(value)}
									</p>
								))}
							</div>
						</div>
					)}
					{step.operation_type === "IaC" && (
						<div>
							<h4 className="font-medium mb-2">IaC Configuration</h4>
							<div className="space-y-2 text-sm">
								{Object.entries(step.configuration).map(([key, value]) => (
									<p key={key}>
										<span className="text-muted-foreground">{key}:</span> {String(value)}
									</p>
								))}
							</div>
						</div>
					)}
				</div>
			),
		});
	};

	const onDragEnd = (result: any) => {
		if (!result.destination) return;

		const items = Array.from(steps);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Call parent handler to persist changes
		onReorderSteps(items);
	};

	const renderStep = (step: RecoveryStep, index: number) => {
		const stepContent = (
			<div className="flex flex-col p-4 bg-card rounded-lg border">
				{/* Main content with improved responsive layout */}
				<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
					{/* Step info and badges - stacks on mobile, side by side on larger screens */}
					<div className="flex flex-col sm:flex-row sm:items-center gap-3 w-full">
						<div className="flex items-center gap-2 min-w-0">
							<GripVertical className="h-5 w-5 text-muted-foreground flex-shrink-0" />
							<span className="font-medium truncate">{step.name}</span>
						</div>

						{/* Badges in a scrollable container on small screens */}
						<div className="flex flex-wrap gap-1.5 mt-1 sm:mt-0">
							<Badge variant="outline" className="text-xs py-0 h-5">
								{step.operation_type}
							</Badge>
						</div>
					</div>

					{/* Action buttons with improved responsive layout */}
					<div className="flex flex-wrap items-center gap-1.5 mt-1 sm:mt-0 sm:flex-nowrap sm:flex-shrink-0">
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										size="icon"
										className="h-8 w-8"
										onClick={() => handleInfoClick(step)}
									>
										<Info className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>View step details</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>

						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										size="icon"
										className="h-8 w-8"
										onClick={() => handleEditStep(step)}
									>
										<Pencil className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>Edit step</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>

						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										size="icon"
										className="h-8 w-8 text-destructive hover:text-destructive"
										onClick={() => handleDeleteStep(step)}
									>
										<Trash className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>Delete step</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					</div>
				</div>

				{/* Step-specific components */}
				{step.operation_type === "IaC" && (
					<div className="mt-3">
						<IaCRecoveryStep step={step} onEdit={() => handleEditStep(step)} />
					</div>
				)}

				{step.operation_type === "Restore virtual machine" && (
					<div className="mt-3">
						<VMRestoreStep step={step} onEdit={() => handleEditStep(step)} />
					</div>
				)}
			</div>
		);

		return (
			<Draggable key={step.id} draggableId={step.id} index={index}>
				{(provided, snapshot) => (
					<div
						ref={provided.innerRef}
						{...provided.draggableProps}
						{...provided.dragHandleProps}
						className={`transition-all duration-200 ${
							snapshot.isDragging ? "scale-105 shadow-lg" : ""
						}`}
					>
						<div className={`transition-all duration-200 ${snapshot.isDragging ? "rotate-2" : ""}`}>
							{stepContent}
						</div>
					</div>
				)}
			</Draggable>
		);
	};

	return (
		<div className="space-y-4 w-full">
			{/* Execution System - Show when execution interface should be displayed */}
			{showExecutionInterface && (
				<ExecutionProgress
					planId={planId}
					datacenterId={datacenterId}
					onExecutionComplete={() => {
						// Execution completed
						console.log("Execution completed");
					}}
				/>
			)}

			{/* Step Management - Only show when not in execution mode */}
			{showStepManagement && (
				<>
					{steps.length === 0 ? (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							className="text-center py-8 px-4 bg-card/50 rounded-lg border border-border"
						>
							<AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
							<p className="text-lg font-medium mb-2">No recovery steps added yet</p>
							<p className="text-muted-foreground mb-6">
								Add steps to create your recovery plan workflow
							</p>
							<Button
								onClick={handleAddStep}
								size="sm"
								variant="default"
								className="bg-dr-purple hover:bg-dr-purple-dark"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add First Step
							</Button>
						</motion.div>
					) : (
						<DragDropContext onDragEnd={onDragEnd}>
							<Droppable droppableId="steps" type="step" mode="standard" direction="vertical">
								{(provided) => (
									<div
										{...provided.droppableProps}
										ref={provided.innerRef}
										className="space-y-2 w-full"
									>
										{steps.map((step, index) => renderStep(step, index))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</DragDropContext>
					)}
				</>
			)}
		</div>
	);
};

export default RecoveryStepList;
