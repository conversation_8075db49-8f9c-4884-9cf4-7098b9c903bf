import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

export class VerificationExecutor extends BaseStepExecutor {
	constructor() {
		super("Verification");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Processing verification step: ${step.name}`);

		try {
			this.validate(step);

			// Verification steps typically require manual confirmation
			return this.createSuccessResult("Verification step ready for manual confirmation", {
				requires_manual_verification: true,
			});
		} catch (error: any) {
			return this.createErrorResult(`Verification failed: ${error.message}`, error.message);
		}
	}

	validate(step: any): boolean {
		super.validate(step);
		if (step.operation_type !== "Verification") {
			throw new Error(`Invalid operation type for verification: ${step.operation_type}`);
		}
		return true;
	}
}
