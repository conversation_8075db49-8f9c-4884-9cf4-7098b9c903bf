import { supabase } from "./supabaseService";
import { sendStepUpdate, sendPlanUpdate } from "../routes/sseManager/sseManager";
import { v4 as uuidv4 } from "uuid";
import { JobQueue, Job } from "./jobQueue";
import { ProgressTracker } from "./progressTracker";
import { StepExecutorFactory } from "./stepExecutors/stepExecutorFactory";
import { createApprovalRequest } from "./approvalService";
import {
	createRecoveryPlanProgress,
	updateRecoveryPlanProgress,
	createRecoveryPlanCheckpoint,
	isCheckpointApproved,
} from "./recoveryProgressService";

export interface ExecutionContext {
	planId: string;
	executionId: string;
	datacenterId: string;
	userId: string;
	steps: any[];
	currentStepIndex: number;
	checkpointId?: string;
}

export interface ExecutionJob {
	id: string;
	type: "start_execution" | "resume_execution" | "execute_step";
	context: ExecutionContext;
	priority: number;
	createdAt: Date;
	scheduledAt?: Date;
}

/**
 * Main execution engine for recovery plans
 * Handles orchestration, job queuing, and progress tracking
 */
export class ExecutionEngine {
	private jobQueue: JobQueue;
	private progressTracker: ProgressTracker;
	private stepExecutorFactory: StepExecutorFactory;
	private activeExecutions: Map<string, ExecutionContext> = new Map();

	constructor() {
		this.jobQueue = new JobQueue();
		this.progressTracker = new ProgressTracker();
		this.stepExecutorFactory = new StepExecutorFactory();

		// Initialize job processing
		this.initializeJobProcessing();
	}

	/**
	 * Start execution of a recovery plan
	 */
	async startExecution(planId: string, datacenterId: string, userId: string): Promise<string> {
		console.log(`Starting execution for plan ${planId}`);

		try {
			// Get recovery plan and steps
			const { data: plan, error: planError } = await supabase
				.from("recovery_plans_new")
				.select("*")
				.eq("id", planId)
				.single();

			if (planError || !plan) {
				throw new Error(`Recovery plan not found: ${planError?.message}`);
			}

			const { data: steps, error: stepsError } = await supabase
				.from("recovery_steps_new")
				.select("*")
				.eq("recovery_plan_id", planId)
				.order("step_order", { ascending: true });

			if (stepsError) {
				throw new Error(`Failed to fetch steps: ${stepsError.message}`);
			}

			if (!steps || steps.length === 0) {
				throw new Error("No steps found for this recovery plan");
			}

			// Create new execution ID
			const executionId = uuidv4();

			// Update plan with execution details
			await this.updatePlanExecutionStatus(planId, executionId, "in_progress", {
				datacenter_id: datacenterId,
				started_by: userId,
				started_at: new Date().toISOString(),
			});

			// Create execution context
			const context: ExecutionContext = {
				planId,
				executionId,
				datacenterId,
				userId,
				steps,
				currentStepIndex: 0,
			};

			// Store active execution
			this.activeExecutions.set(executionId, context);

			// Initialize progress tracking
			await this.progressTracker.initializeExecution(planId, executionId, steps);

			// Queue execution job
			const job: ExecutionJob = {
				id: uuidv4(),
				type: "start_execution",
				context,
				priority: 1,
				createdAt: new Date(),
			};

			await this.jobQueue.enqueue(job);

			// Send initial SSE update
			sendPlanUpdate(planId, "STARTED", {
				execution_id: executionId,
				total_steps: steps.length,
				message: "Recovery plan execution started",
			});

			return executionId;
		} catch (error) {
			console.error(`Error starting execution for plan ${planId}:`, error);
			throw error;
		}
	}

	/**
	 * Resume execution from a checkpoint
	 */
	async resumeExecution(planId: string, checkpointId: string, datacenterId: string): Promise<void> {
		console.log(`Resuming execution for plan ${planId} from checkpoint ${checkpointId}`);

		try {
			// Get current execution context
			const { data: plan, error: planError } = await supabase
				.from("recovery_plans_new")
				.select("*")
				.eq("id", planId)
				.single();

			if (planError || !plan) {
				throw new Error(`Recovery plan not found: ${planError?.message}`);
			}

			const executionId = plan.current_execution_id;
			if (!executionId) {
				throw new Error("No active execution found for this plan");
			}

			// Get checkpoint details
			const { data: checkpoint, error: checkpointError } = await supabase
				.from("recovery_plan_checkpoints")
				.select("*")
				.eq("id", checkpointId)
				.single();

			if (checkpointError || !checkpoint) {
				throw new Error(`Checkpoint not found: ${checkpointError?.message}`);
			}

			// Get steps and find current position
			const { data: steps, error: stepsError } = await supabase
				.from("recovery_steps_new")
				.select("*")
				.eq("recovery_plan_id", planId)
				.order("step_order", { ascending: true });

			if (stepsError || !steps) {
				throw new Error(`Failed to fetch steps: ${stepsError?.message}`);
			}

			const currentStepIndex = steps.findIndex((step) => step.id === checkpoint.step_id);
			if (currentStepIndex === -1) {
				throw new Error("Checkpoint step not found in plan");
			}

			// Create execution context
			const context: ExecutionContext = {
				planId,
				executionId,
				datacenterId,
				userId: plan.created_by,
				steps,
				currentStepIndex,
				checkpointId,
			};

			// Store active execution
			this.activeExecutions.set(executionId, context);

			// Update plan status
			await this.updatePlanExecutionStatus(planId, executionId, "in_progress", {
				...plan.execution_metadata,
				resumed_at: new Date().toISOString(),
				resumed_from_checkpoint: checkpointId,
			});

			// Queue resume job
			const job: ExecutionJob = {
				id: uuidv4(),
				type: "resume_execution",
				context,
				priority: 1,
				createdAt: new Date(),
			};

			await this.jobQueue.enqueue(job);

			// Send SSE update
			sendPlanUpdate(planId, "RESUMED", {
				execution_id: executionId,
				checkpoint_id: checkpointId,
				message: "Recovery plan execution resumed",
			});
		} catch (error) {
			console.error(`Error resuming execution for plan ${planId}:`, error);
			throw error;
		}
	}

	/**
	 * Update plan execution status
	 */
	private async updatePlanExecutionStatus(
		planId: string,
		executionId: string,
		status: string,
		metadata: any = {}
	): Promise<void> {
		const updateData: any = {
			current_execution_id: executionId,
			execution_status: status,
			execution_metadata: metadata,
		};

		if (status === "in_progress") {
			updateData.execution_started_at = new Date().toISOString();
		} else if (status === "completed" || status === "failed") {
			updateData.execution_completed_at = new Date().toISOString();
		}

		const { error } = await supabase.from("recovery_plans_new").update(updateData).eq("id", planId);

		if (error) {
			throw new Error(`Failed to update plan status: ${error.message}`);
		}
	}

	/**
	 * Initialize job processing
	 */
	private initializeJobProcessing(): void {
		this.jobQueue.onJob(async (job: Job) => {
			const executionJob = job as ExecutionJob;
			try {
				await this.processJob(executionJob);
			} catch (error: any) {
				console.error(`Error processing job ${job.id}:`, error);
				await this.handleJobError(executionJob, error);
			}
		});
	}

	/**
	 * Process a job
	 */
	private async processJob(job: ExecutionJob): Promise<void> {
		console.log(`Processing job ${job.id} of type ${job.type}`);

		switch (job.type) {
			case "start_execution":
			case "resume_execution":
				await this.executeSteps(job.context);
				break;
			case "execute_step":
				await this.executeSingleStep(job.context);
				break;
			default:
				throw new Error(`Unknown job type: ${job.type}`);
		}
	}

	/**
	 * Execute steps in sequence
	 */
	private async executeSteps(context: ExecutionContext): Promise<void> {
		const { planId, executionId, steps, currentStepIndex } = context;

		for (let i = currentStepIndex; i < steps.length; i++) {
			const step = steps[i];
			context.currentStepIndex = i;

			try {
				// Check if step requires approval
				if (step.requires_approval) {
					const isApproved = await this.checkApprovalStatus(step, executionId);
					if (!isApproved) {
						await this.handleApprovalRequired(step, context);
						return; // Pause execution
					}
				}

				// Execute the step
				await this.executeSingleStep(context);
			} catch (error: any) {
				console.error(`Error executing step ${step.name}:`, error);
				await this.handleStepError(step, context, error);
				return;
			}
		}

		// All steps completed
		await this.completeExecution(context);
	}

	/**
	 * Execute a single step
	 */
	private async executeSingleStep(context: ExecutionContext): Promise<void> {
		const { planId, executionId, datacenterId, steps, currentStepIndex } = context;
		const step = steps[currentStepIndex];

		console.log(`Executing step ${step.name} (${step.operation_type})`);

		// Update step status to in_progress
		await this.progressTracker.updateStepStatus(planId, step.id, executionId, "in_progress");

		// Send SSE update
		sendStepUpdate(planId, step.step_order.toString(), "IN_PROGRESS", {
			step_name: step.name,
			operation_type: step.operation_type,
		});

		try {
			// Get step executor
			const executor = this.stepExecutorFactory.getExecutor(step.operation_type);

			// Execute the step
			const result = await executor.execute(step, {
				datacenterId,
				executionId,
				planId,
			});

			// Update step status to completed
			await this.progressTracker.updateStepStatus(planId, step.id, executionId, "completed", {
				result,
				completed_at: new Date().toISOString(),
			});

			// Send SSE update
			sendStepUpdate(planId, step.step_order.toString(), "COMPLETED", {
				step_name: step.name,
				result,
			});
		} catch (error: any) {
			console.error(`Step execution failed for ${step.name}:`, error);

			// Update step status to failed
			await this.progressTracker.updateStepStatus(planId, step.id, executionId, "failed", {
				error: error.message,
				failed_at: new Date().toISOString(),
			});

			// Send SSE update
			sendStepUpdate(planId, step.step_order.toString(), "FAILED", {
				step_name: step.name,
				error: error.message,
			});

			throw error;
		}
	}

	/**
	 * Check if step approval is satisfied
	 */
	private async checkApprovalStatus(step: any, executionId: string): Promise<boolean> {
		try {
			// Check if there's an existing checkpoint for this step
			const { data: checkpoint, error } = await supabase
				.from("recovery_plan_checkpoints")
				.select("*")
				.eq("step_id", step.id)
				.eq("recovery_plan_id", step.recovery_plan_id)
				.single();

			if (error && error.code !== "PGRST116") {
				// PGRST116 = no rows returned
				console.error("Error checking checkpoint:", error);
				return false;
			}

			if (!checkpoint) {
				return false; // No checkpoint exists, approval needed
			}

			return checkpoint.approval_status === "approved";
		} catch (error) {
			console.error("Error checking approval status:", error);
			return false;
		}
	}

	/**
	 * Handle approval required for a step
	 */
	private async handleApprovalRequired(step: any, context: ExecutionContext): Promise<void> {
		const { planId, executionId } = context;

		console.log(`Step ${step.name} requires approval, pausing execution`);

		// Update step status to awaiting_approval
		await this.progressTracker.updateStepStatus(planId, step.id, executionId, "awaiting_approval");

		// Create or update checkpoint
		const checkpoint = await createRecoveryPlanCheckpoint(
			step.recovery_plan_id,
			step.id,
			step.approval_metadata?.approver_id || "admin",
			step.approval_metadata?.approver_role || "admin",
			{
				datacenter_id: context.datacenterId,
				execution_id: executionId,
				created_at: new Date().toISOString(),
			}
		);

		// Create approval request
		await createApprovalRequest(step, checkpoint);

		// Update plan status to paused
		await this.updatePlanExecutionStatus(planId, executionId, "paused", {
			paused_at: new Date().toISOString(),
			paused_reason: "awaiting_approval",
			current_checkpoint_id: checkpoint.id,
			current_step_id: step.id,
		});

		// Remove from active executions
		this.activeExecutions.delete(executionId);

		// Send SSE updates
		sendStepUpdate(planId, step.step_order.toString(), "AWAITING_APPROVAL", {
			step_name: step.name,
			checkpoint_id: checkpoint.id,
			approver_id: step.approval_metadata?.approver_id,
			approver_role: step.approval_metadata?.approver_role,
		});

		sendPlanUpdate(planId, "PAUSED", {
			execution_id: executionId,
			checkpoint_id: checkpoint.id,
			message: "Execution paused - awaiting approval",
		});
	}

	/**
	 * Handle step execution error
	 */
	private async handleStepError(step: any, context: ExecutionContext, error: any): Promise<void> {
		const { planId, executionId } = context;

		console.error(`Step ${step.name} failed:`, error);

		// Update plan status to failed
		await this.updatePlanExecutionStatus(planId, executionId, "failed", {
			failed_at: new Date().toISOString(),
			failed_step_id: step.id,
			error: error.message,
		});

		// Remove from active executions
		this.activeExecutions.delete(executionId);

		// Send SSE update
		sendPlanUpdate(planId, "FAILED", {
			execution_id: executionId,
			failed_step: step.name,
			error: error.message,
			message: "Recovery plan execution failed",
		});
	}

	/**
	 * Complete execution
	 */
	private async completeExecution(context: ExecutionContext): Promise<void> {
		const { planId, executionId } = context;

		console.log(`Completing execution for plan ${planId}`);

		// Update plan status to completed
		await this.updatePlanExecutionStatus(planId, executionId, "completed", {
			completed_at: new Date().toISOString(),
		});

		// Remove from active executions
		this.activeExecutions.delete(executionId);

		// Send SSE update
		sendPlanUpdate(planId, "COMPLETED", {
			execution_id: executionId,
			message: "Recovery plan execution completed successfully",
		});
	}

	/**
	 * Handle job error
	 */
	private async handleJobError(job: ExecutionJob, error: any): Promise<void> {
		console.error(`Job ${job.id} failed:`, error);

		const { planId, executionId } = job.context;

		// Update plan status to failed
		await this.updatePlanExecutionStatus(planId, executionId, "failed", {
			failed_at: new Date().toISOString(),
			error: error.message,
			job_id: job.id,
		});

		// Remove from active executions
		this.activeExecutions.delete(executionId);

		// Send SSE update
		sendPlanUpdate(planId, "FAILED", {
			execution_id: executionId,
			error: error.message,
			message: "Recovery plan execution failed",
		});
	}

	/**
	 * Get execution status
	 */
	async getExecutionStatus(planId: string): Promise<any> {
		const { data: plan, error } = await supabase
			.from("recovery_plans_new")
			.select("*")
			.eq("id", planId)
			.single();

		if (error || !plan) {
			throw new Error(`Plan not found: ${error?.message}`);
		}

		return {
			planId,
			executionId: plan.current_execution_id,
			status: plan.execution_status,
			metadata: plan.execution_metadata,
			isActive: this.activeExecutions.has(plan.current_execution_id),
		};
	}
}

// Singleton instance
export const executionEngine = new ExecutionEngine();
