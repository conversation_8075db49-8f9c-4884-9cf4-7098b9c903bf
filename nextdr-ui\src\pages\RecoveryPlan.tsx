import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Plus, RefreshCw, Loader2, <PERSON>ci<PERSON>, Trash } from "lucide-react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import PageLayout from "@/components/layout/PageLayout";
import { RecoveryPlan, RecoveryStep } from "@/lib/types";
import { useModalStore } from "@/lib/store/useStore";
import RecoveryPlanModal from "@/components/modals/RecoveryPlanModal";
import RecoveryStepModal from "@/components/modals/RecoveryStepModal";
import StepInfoModal from "@/components/modals/StepInfoModal";
import CheckpointInfoModal from "@/components/modals/CheckpointInfoModal";
import ConfirmDialog from "@/components/modals/ConfirmDialog";
import ExecutionOptionsModal from "@/components/modals/ExecutionOptionsModal";
import RecoveryStepList from "@/components/recovery/RecoveryStepList";
import {
	useRecoveryPlans,
	useRecoverySteps,
	useExecuteRecoveryPlan,
	useUpdateRecoveryStep,
	useDeleteRecoveryPlan,
	useUpdateRecoveryPlan,
	useDeleteRecoveryStep,
	useRecoveryPlanExecution,
} from "@/lib/api/hooks/recoveryPlans";
import { useApplicationGroups } from "@/lib/api/hooks/applicationGroups";
import { useDatacenters } from "@/lib/api/hooks/datacenters";
import { toast } from "@/components/ui/sonner";

const RecoveryPlanPage = () => {
	const { planId } = useParams<{ planId?: string }>();
	const navigate = useNavigate();
	const { onOpen } = useModalStore();
	const [selectedPlan, setSelectedPlan] = useState<RecoveryPlan | null>(null);
	const [editingStep, setEditingStep] = useState<RecoveryStep | null>(null);
	const [config, setConfig] = useState<Record<string, any>>({});
	const [localSteps, setLocalSteps] = useState<RecoveryStep[]>([]);

	const { data: recoveryPlans = [], isLoading: isLoadingPlans } = useRecoveryPlans();
	const { data: appGroups = [] } = useApplicationGroups();
	const { data: recoverySteps = [], isLoading: isLoadingSteps } = useRecoverySteps(
		selectedPlan?.id || ""
	);
	const { data: datacenters = [] } = useDatacenters();
	const executePlanMutation = useExecuteRecoveryPlan();
	const updateStepMutation = useUpdateRecoveryStep();
	const deletePlanMutation = useDeleteRecoveryPlan();
	const deleteStepMutation = useDeleteRecoveryStep();

	useEffect(() => {
		const filteredSteps = recoverySteps.filter((step) => step.recovery_plan_id === selectedPlan.id);

		const currentStepsString = JSON.stringify(localSteps);
		const newStepsString = JSON.stringify(filteredSteps);

		if (currentStepsString !== newStepsString) {
			setLocalSteps(filteredSteps);
		}
	}, [recoverySteps, selectedPlan?.id]);

	// Handle URL parameter to auto-select plan
	useEffect(() => {
		if (planId && recoveryPlans.length > 0 && !selectedPlan) {
			const plan = recoveryPlans.find((p) => p.id === planId);
			if (plan) {
				setupPlanConfig(plan);
			}
		}
	}, [planId, recoveryPlans, selectedPlan]);

	const handleSelectPlan = (plan: RecoveryPlan) => {
		// Navigate to the plan's URL
		navigate(`/recoveryplan/${plan.id}`);
	};

	const setupPlanConfig = (plan: RecoveryPlan) => {
		setSelectedPlan(plan);
		let selectedAppGroup = appGroups.find((group) => group.id === plan.app_group_id);
		let selectedDatacenter = datacenters.find(
			(dc) => dc.project_id === selectedAppGroup?.data_center_id
		);
		if (selectedDatacenter) {
			setConfig({
				project_id: selectedDatacenter?.project_id,
				zone: selectedDatacenter?.zone || "us-central1-c",
				datacenter_id: selectedDatacenter?.id,
			});
		}
		setEditingStep(null);
	};

	const handleEditStep = (step: RecoveryStep) => {
		setEditingStep(step);
		onOpen("recoveryStep", {
			step,
			planId: selectedPlan?.id,
			config,
		});
	};

	const handleExecutePlan = () => {
		if (selectedPlan && config.datacenter_id) {
			executePlanMutation.mutate({
				planId: selectedPlan.id,
				datacenterId: config.datacenter_id,
			});
		}
	};

	const handleEditPlan = (plan: RecoveryPlan) => {
		setSelectedPlan(plan);
		onOpen("recoveryPlan", { plan });
	};

	const handleDeletePlan = async (plan: RecoveryPlan) => {
		onOpen("confirmDialog", {
			title: "Delete Recovery Plan",
			message: `Are you sure you want to delete the recovery plan "${plan.name}"? This action cannot be undone.`,
			confirmLabel: "Delete",
			cancelLabel: "Cancel",
			variant: "destructive",
			onConfirm: async () => {
				try {
					await deletePlanMutation.mutateAsync(plan.id);

					if (selectedPlan?.id === plan.id) {
						setSelectedPlan(null);
						setLocalSteps([]);
					}

					toast.success(`Recovery plan "${plan.name}" deleted successfully`);
				} catch (error) {
					console.error("Error deleting recovery plan:", error);
					toast.error("Failed to delete recovery plan");
				}
			},
		});
	};

	const handleReorderSteps = (newSteps: RecoveryStep[]) => {
		if (!selectedPlan) return;

		setLocalSteps(newSteps);

		const updatePromises = newSteps.map((step, index) => {
			if (step.step_order !== index + 1) {
				const updatedStep = {
					...step,
					step_order: index + 1,
				};
				return updateStepMutation.mutateAsync(updatedStep);
			}
			return Promise.resolve();
		});

		Promise.all(updatePromises)
			.then(() => {
				toast.success("Steps reordered successfully");
			})
			.catch((error) => {
				console.error("Error reordering steps:", error);
				toast.error("Failed to reorder steps");
				const filteredSteps = recoverySteps.filter(
					(step) => step.recovery_plan_id === selectedPlan.id
				);
				setLocalSteps(filteredSteps);
			});
	};

	const getAppGroupById = (id: string) => {
		return appGroups.find((group) => group.id === id);
	};

	const container = {
		hidden: { opacity: 0 },
		show: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1,
			},
		},
	};

	const item = {
		hidden: { opacity: 0, y: 20 },
		show: { opacity: 1, y: 0, transition: { duration: 0.4 } },
	};

	return (
		<PageLayout title="Recovery Plans">
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-2xl font-bold">Recovery Plans</h1>
					<p className="text-muted-foreground">Design and execute your disaster recovery plans</p>
				</div>
				<Button
					className="bg-dr-purple hover:bg-dr-purple-dark"
					onClick={() => onOpen("recoveryPlan")}
				>
					<Plus className="mr-2 h-4 w-4" />
					New Recovery Plan
				</Button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
				<motion.div
					variants={container}
					initial="hidden"
					animate="show"
					className="lg:col-span-1 space-y-4"
				>
					{isLoadingPlans ? (
						<div className="flex justify-center items-center py-8">
							<Loader2 className="h-6 w-6 animate-spin mr-2" />
							<span>Loading recovery plans...</span>
						</div>
					) : recoveryPlans.length > 0 ? (
						recoveryPlans.map((plan) => (
							<motion.div
								key={plan.id}
								variants={item}
								onClick={() => handleSelectPlan(plan as RecoveryPlan)}
								className={`cursor-pointer transition-colors ${
									selectedPlan?.id === plan.id ? "border-dr-purple" : "border-border"
								}`}
							>
								<Card
									className={`overflow-hidden ${
										selectedPlan?.id === plan.id ? "border-dr-purple bg-secondary/50" : "bg-card"
									}`}
								>
									<CardContent className="p-4">
										<div className="flex items-center justify-between gap-3">
											<div className="flex items-center gap-3 min-w-0">
												<div className="flex-shrink-0 p-2 rounded-full bg-green-900/20 text-green-500">
													<RefreshCw className="h-5 w-5" />
												</div>
												<div className="min-w-0">
													<div className="flex items-center space-x-2">
														<h3 className="font-medium truncate">{plan.name}</h3>
														{/* {getStatusBadge(plan.status)} */}
													</div>
													<div className="text-xs text-muted-foreground truncate">
														{getAppGroupById(plan.app_group_id)?.name || "Unknown"}
													</div>
												</div>
											</div>
											<div className="flex items-center gap-1.5 flex-shrink-0">
												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger asChild>
															<Button
																variant="ghost"
																size="icon"
																className="h-8 w-8"
																onClick={(e) => {
																	e.stopPropagation();
																	handleEditPlan(plan as RecoveryPlan);
																}}
															>
																<Pencil className="h-4 w-4" />
															</Button>
														</TooltipTrigger>
														<TooltipContent>
															<p>Edit plan</p>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>

												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger asChild>
															<Button
																variant="ghost"
																size="icon"
																className="h-8 w-8 text-destructive hover:text-destructive"
																onClick={(e) => {
																	e.stopPropagation();
																	handleDeletePlan(plan as RecoveryPlan);
																}}
															>
																<Trash className="h-4 w-4" />
															</Button>
														</TooltipTrigger>
														<TooltipContent>
															<p>Delete plan</p>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						))
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<p>No recovery plans found</p>
							<Button
								onClick={() => onOpen("recoveryPlan")}
								variant="outline"
								size="sm"
								className="mt-2"
							>
								Create Recovery Plan
							</Button>
						</div>
					)}
				</motion.div>

				<div className="lg:col-span-3">
					{selectedPlan ? (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
						>
							<Card>
								<CardContent className="p-6">
									<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
										<div className="flex items-center min-w-0">
											<div className="p-2 rounded-full bg-green-900/20 mr-4 flex-shrink-0">
												<RefreshCw className="h-5 w-5 text-green-500" />
											</div>
											<div className="min-w-0">
												<div className="flex items-center space-x-2">
													<h2 className="text-xl font-bold truncate">{selectedPlan.name}</h2>
													{/* {getStatusBadge(selectedPlan.status)} */}
												</div>
												<p className="text-sm text-muted-foreground truncate">
													App Group: {getAppGroupById(selectedPlan.app_group_id)?.name || "Unknown"}
												</p>
											</div>
										</div>
										<div className="flex items-center">
											<Button
												variant="default"
												size="sm"
												className="bg-dr-purple hover:bg-dr-purple-dark"
												onClick={() => onOpen("recoveryStep", { planId: selectedPlan.id })}
												disabled={isLoadingSteps}
											>
												<Plus className="mr-1 h-4 w-4" />
												Add Step
											</Button>
										</div>
									</div>

									<div className="space-y-4">
										{isLoadingSteps ? (
											<div className="flex justify-center items-center py-8">
												<Loader2 className="h-6 w-6 animate-spin mr-2" />
												<span>Loading recovery steps...</span>
											</div>
										) : (
											<RecoveryStepList
												steps={localSteps}
												planId={selectedPlan.id}
												datacenterId={config.datacenter_id}
												// onAddStep={() => onOpen("recoveryStep" , {planId: selectedPlan.id})}
												onEditStep={handleEditStep}
												onReorderSteps={handleReorderSteps}
												onDeleteStep={(step) => {
													console.log(`Step "${step.name}" deleted`);
												}}
											/>
										)}
									</div>
								</CardContent>
							</Card>
						</motion.div>
					) : (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
							className="h-full flex items-center justify-center"
						>
							<div className="text-center">
								<RefreshCw className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
								<h3 className="text-lg font-medium mb-1">No recovery plan selected</h3>
								<p className="text-muted-foreground mb-4">
									Select a recovery plan or create a new one
								</p>
								<Button
									className="bg-dr-purple hover:bg-dr-purple-dark"
									onClick={() => onOpen("recoveryPlan")}
								>
									<Plus className="mr-2 h-4 w-4" />
									New Recovery Plan
								</Button>
							</div>
						</motion.div>
					)}
				</div>
			</div>

			<RecoveryPlanModal />
			<RecoveryStepModal />
			<StepInfoModal />
			<CheckpointInfoModal />
			<ConfirmDialog />
			<ExecutionOptionsModal />
		</PageLayout>
	);
};

export default RecoveryPlanPage;
