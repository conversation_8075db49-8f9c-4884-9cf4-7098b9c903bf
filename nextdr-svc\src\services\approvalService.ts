import { supabase } from "../services/supabaseService";
import { RecoverySteps } from "../models/gcp_backup";
import { v4 as uuidv4 } from "uuid";
import { sendApprovalEmail } from "./emailService";
import { config } from "../config";
import {
	updateCheckpointApprovalStatus,
	resumeRecoveryPlanExecution,
} from "./recoveryProgressService";

interface ApprovalToken {
	id: string;
	step_id: string;
	approver_id: string;
	token: string;
	expires_at: string;
	created_at: string;
	is_used?: boolean;
	used_at?: string;
	used_by?: string;
	is_checkpoint?: boolean;
	checkpoint_id?: string;
}

interface Checkpoint {
	id: string;
	recovery_plan_id: string;
	step_id: string;
	approver_id?: string;
	approver_role?: string;
	approval_required: boolean;
	approval_status?: string;
	approved_at?: string;
	approved_by?: string;
	approval_metadata?: Record<string, any>;
}

/**
 * Create an approval request for a step
 * @param step The recovery step requiring approval
 * @param checkpoint Optional checkpoint information
 */
export const createApprovalRequest = async (step: RecoverySteps, checkpoint?: Checkpoint) => {
	// Determine the approver ID
	let approverId: string | undefined;
	let checkpointId: string | undefined;

	if (checkpoint) {
		// If a checkpoint is provided, use its approver information
		approverId = checkpoint.approver_id;
		checkpointId = checkpoint.id;

		// If checkpoint uses role-based approval, find a user with that role
		if (!approverId && checkpoint.approver_role) {
			const { data: approvers, error: approversError } = await supabase
				.from("user_profiles")
				.select("id")
				.eq("role", checkpoint.approver_role)
				.eq("status", "active")
				.limit(1);

			if (approversError) {
				console.error("Error finding approver by role:", approversError);
				throw new Error(
					`Failed to find approver with role ${checkpoint.approver_role}: ${approversError.message}`
				);
			}

			if (approvers && approvers.length > 0) {
				approverId = approvers[0].id;
			}
		}
	} else {
		// Legacy flow - use step's approval metadata
		approverId = step.approval_metadata?.approver_id;

		// If this step doesn't have an approver directly assigned, find the most recent approval step
		if (!approverId) {
			// Get all steps for this recovery plan
			const { data: allSteps, error: stepsError } = await supabase
				.from("recovery_steps_new")
				.select("*")
				.eq("recovery_plan_id", step.recovery_plan_id)
				.order("step_order", { ascending: true });

			if (stepsError) {
				throw new Error(`Failed to get recovery steps: ${stepsError.message}`);
			}

			// Find the most recent approval/verification step before the current step
			const approvalSteps = allSteps
				.filter(
					(s) =>
						(s.operation_type === "Approval" || s.operation_type === "Verification") &&
						s.step_order < step.step_order
				)
				.sort((a, b) => b.step_order - a.step_order); // Sort in descending order

			if (approvalSteps.length > 0) {
				approverId = approvalSteps[0].approval_metadata?.approver_id;
			}
		}
	}

	// If no approver is found, try to find an admin user as a fallback
	if (!approverId) {
		console.log("No specific approver found for this step, looking for admin users...");

		// Find admin users
		const { data: admins, error: adminsError } = await supabase
			.from("user_profiles")
			.select("id, email")
			.eq("role", "admin")
			.eq("status", "active")
			.limit(1);

		if (adminsError) {
			console.error("Error finding admin users:", adminsError);
			throw new Error(`Failed to find admin users: ${adminsError.message}`);
		}

		if (admins && admins.length > 0) {
			approverId = admins[0].id;
			console.log(`Using admin user ${admins[0].email} (${approverId}) as approver`);
		} else {
			// If no admin users found, try to find any active user
			console.log("No admin users found, looking for any active user...");
			const { data: users, error: usersError } = await supabase
				.from("user_profiles")
				.select("id, email")
				.eq("status", "active")
				.limit(1);

			if (usersError) {
				console.error("Error finding active users:", usersError);
				throw new Error(`Failed to find active users: ${usersError.message}`);
			}

			if (users && users.length > 0) {
				approverId = users[0].id;
				console.log(`Using active user ${users[0].email} (${approverId}) as approver`);
			} else {
				throw new Error("No approver found for this step and no fallback users available");
			}
		}
	}

	// Check if an active token already exists for this step/checkpoint
	let token: string;
	let expiresAt: Date;

	// Query parameters for finding existing tokens
	const queryParams: any = {
		step_id: step.id,
		approver_id: approverId,
		is_used: false,
	};

	// If this is a checkpoint-based approval, include the checkpoint ID
	if (checkpointId) {
		queryParams.is_checkpoint = true;
		queryParams.checkpoint_id = checkpointId;
	}

	// Check for existing tokens
	const { data: existingTokens, error: existingTokenError } = await supabase
		.from("approval_tokens")
		.select("*")
		.match(queryParams)
		.gt("expires_at", new Date().toISOString());

	if (existingTokenError) {
		console.error("Error checking for existing tokens:", existingTokenError);
	}

	// If a valid token exists, use it
	if (existingTokens && existingTokens.length > 0) {
		console.log(`Using existing approval token for step ${step.id}`);
		token = existingTokens[0].token;
		expiresAt = new Date(existingTokens[0].expires_at);
	} else {
		// Create a new token
		token = uuidv4();
		expiresAt = new Date();
		expiresAt.setHours(expiresAt.getHours() + 24); // Token expires in 24 hours

		const tokenInsert: any = {
			step_id: step.id,
			approver_id: approverId,
			token,
			expires_at: expiresAt.toISOString(),
			is_used: false,
		};

		// If this is a checkpoint-based approval, include the checkpoint ID
		if (checkpointId) {
			tokenInsert.is_checkpoint = true;
			tokenInsert.checkpoint_id = checkpointId;
		}

		const { data: tokenData, error: tokenError } = await supabase
			.from("approval_tokens")
			.insert(tokenInsert);

		if (tokenError) {
			// If the error is a duplicate key error, try to get the existing token
			if (tokenError.code === "23505") {
				// PostgreSQL unique violation code
				console.log(`Duplicate key error for step ${step.id}, trying to get existing token`);
				const { data: existingToken, error: getError } = await supabase
					.from("approval_tokens")
					.select("*")
					.match(queryParams)
					.single();

				if (getError || !existingToken) {
					throw new Error(`Failed to create or get approval token: ${tokenError.message}`);
				}

				token = existingToken.token;
				expiresAt = new Date(existingToken.expires_at);
			} else {
				throw new Error(`Failed to create approval token: ${tokenError.message}`);
			}
		}
	}

	// Check if we need to send an email notification
	// If this is a checkpoint and the checkpoint already exists, we might not need to send another email
	let shouldSendEmail = true;

	if (checkpointId) {
		// Check if this checkpoint already has a pending approval request
		const { data: checkpointData, error: checkpointError } = await supabase
			.from("recovery_plan_checkpoints")
			.select("*")
			.eq("id", checkpointId)
			.single();

		if (!checkpointError && checkpointData) {
			// If the checkpoint is already approved or rejected, don't send another email
			if (
				checkpointData.approval_status === "approved" ||
				checkpointData.approval_status === "rejected"
			) {
				console.log(
					`Checkpoint ${checkpointId} is already ${checkpointData.approval_status}, not sending email`
				);
				shouldSendEmail = false;
			}

			// If the checkpoint was updated more than 5 minutes ago and is still pending, don't send another email
			// This prevents spamming the approver with emails if the execution is resumed multiple times
			if (checkpointData.approval_status === "pending") {
				// Use updated_at instead of created_at to ensure we send a new email when the checkpoint is reset
				const updatedAt = new Date(checkpointData.updated_at);
				const now = new Date();
				const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

				if (minutesSinceUpdate < 5) {
					console.log(
						`Checkpoint ${checkpointId} was updated less than 5 minutes ago, sending email`
					);
				} else {
					console.log(
						`Checkpoint ${checkpointId} was updated more than 5 minutes ago, not sending another email`
					);
					shouldSendEmail = false;
				}
			}
		}
	}

	// Get approver email
	const { data: approver, error: approverError } = await supabase
		.from("user_profiles")
		.select("email")
		.eq("id", approverId)
		.single();

	if (approverError || !approver) {
		throw new Error(`Failed to get approver email: ${approverError?.message}`);
	}

	// Send approval email
	const approvalUrl = `${config.FRONTEND_URL}/approve/${token}`;

	try {
		// Get the recovery plan name for better context in the email
		const { data: plan, error: planError } = await supabase
			.from("recovery_plans_new")
			.select("name")
			.eq("id", step.recovery_plan_id)
			.single();

		if (planError) {
			console.warn("Could not fetch plan name:", planError.message);
		}

		// Only send email if shouldSendEmail is true
		if (shouldSendEmail) {
			// Determine if this is an approval or verification step
			if (step.operation_type === "Verification") {
				// Import the verification email function
				const { sendVerificationEmail } = await import("./emailService");

				// Send the verification email
				await sendVerificationEmail(
					approver.email,
					step.name,
					plan?.name || "Recovery Plan",
					token
				);
				console.log(`Verification email sent to ${approver.email} for step ${step.name}`);
			} else {
				// Send the approval email
				await sendApprovalEmail(approver.email, step.name, plan?.name || "Recovery Plan", token);
				console.log(`Approval email sent to ${approver.email} for step ${step.name}`);
			}
		} else {
			console.log(`Skipping email notification for step ${step.name} (token: ${token})`);
		}

		console.log("Approval/Verification URL:", approvalUrl);

		// Log the approval request
		await supabase.from("audit_logs").insert({
			action: "send_approval_request",
			entity_type: checkpoint ? "recovery_plan_checkpoint" : "recovery_step",
			entity_id: checkpoint ? checkpoint.id : step.id,
			details: {
				recovery_plan_id: step.recovery_plan_id,
				step_id: step.id,
				approver_id: approverId,
				token: token,
				expires_at: expiresAt.toISOString(),
			},
		});
	} catch (error: any) {
		console.error("Failed to send approval email:", error);
		throw new Error(`Failed to send approval email: ${error.message}`);
	}
};

/**
 * Validate an approval token
 * @param token The approval token
 * @returns The token data if valid
 * @throws Error if token is invalid, expired, or already used
 */
export const validateApprovalToken = async (token: string): Promise<ApprovalToken> => {
	console.log(`Validating approval token: ${token}`);

	// Maximum number of retries
	const maxRetries = 3;

	// Function to validate token with retries
	const validateWithRetry = async (retryCount = 0): Promise<ApprovalToken> => {
		try {
			// Validate token
			const { data: tokenData, error: tokenError } = await supabase
				.from("approval_tokens")
				.select("*")
				.eq("token", token)
				.single();

			if (tokenError) {
				// Check if this is a connection error that we should retry
				if (tokenError.message?.includes("fetch failed") && retryCount < maxRetries) {
					console.log(`Connection error, retrying (${retryCount + 1}/${maxRetries})...`);
					// Wait before retrying (exponential backoff)
					await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
					return validateWithRetry(retryCount + 1);
				}

				console.error(`Token validation error:`, tokenError);
				throw new Error(`Invalid approval token: ${tokenError.message}`);
			}

			if (!tokenData) {
				console.error(`Token not found: ${token}`);
				throw new Error("Token not found");
			}

			console.log(`Token found:`, tokenData);

			// Check if token is expired
			if (new Date(tokenData.expires_at) < new Date()) {
				console.error(`Token expired: ${tokenData.expires_at}`);
				throw new Error("Approval token has expired");
			}

			// Check if token has already been used
			if (tokenData.is_used) {
				console.error(`Token already used: ${token}`);
				throw new Error("This approval token has already been used");
			}

			console.log(`Token validation successful for step: ${tokenData.step_id}`);
			return tokenData as ApprovalToken;
		} catch (error: any) {
			// If this is a connection error and we haven't exceeded max retries
			if (error.message?.includes("fetch failed") && retryCount < maxRetries) {
				console.log(`Connection error, retrying (${retryCount + 1}/${maxRetries})...`);
				// Wait before retrying (exponential backoff)
				await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
				return validateWithRetry(retryCount + 1);
			}

			console.error(`Error validating token: ${error.message}`);
			throw error;
		}
	};

	// Start validation with retries
	return validateWithRetry();
};

/**
 * Process an approval decision
 * @param token The approval token
 * @param decision The approval decision (approved or rejected)
 * @param comment Optional comment
 * @param approverIp The IP address of the approver
 * @param userAgent The user agent of the approver
 * @param authenticatedUserId Optional authenticated user ID (if approval is done through UI)
 * @returns The updated step or checkpoint
 */
export const processApproval = async (
	token: string,
	decision: "approved" | "rejected",
	comment: string,
	approverIp: string,
	userAgent?: string,
	authenticatedUserId?: string
) => {
	console.log(`Processing approval for token ${token}, decision: ${decision}`);

	try {
		// Validate token
		const tokenData = await validateApprovalToken(token);
		console.log(`Token validated successfully: ${JSON.stringify(tokenData)}`);

		// Determine the user ID to use for the approval
		// If authenticated user is provided, use that (for UI approvals)
		// Otherwise use the token's approver_id (for email link approvals)
		const userId = authenticatedUserId || tokenData.approver_id;
		console.log(`Using user ID for approval: ${userId}`);

		// Mark token as used
		await supabase
			.from("approval_tokens")
			.update({
				is_used: true,
				used_at: new Date().toISOString(),
				used_by: userId,
			})
			.eq("id", tokenData.id);

		// Check if this is a checkpoint-based approval
		if (tokenData.is_checkpoint && tokenData.checkpoint_id) {
			// Update the checkpoint approval status
			const checkpoint = await updateCheckpointApprovalStatus(
				tokenData.checkpoint_id,
				decision,
				userId,
				comment,
				{
					ip_address: approverIp,
					user_agent: userAgent,
					token_id: tokenData.id,
				}
			);

			console.log(`Checkpoint ${tokenData.checkpoint_id} approval status updated to ${decision}`);

			// If approved, resume execution
			if (decision === "approved") {
				console.log(
					`Approval decision is 'approved', resuming execution for plan ${checkpoint.recovery_plan_id}`
				);

				// Try to use the new execution engine first
				try {
					const { executionEngine } = await import("../services/executionEngine");

					// Get datacenter ID from checkpoint metadata or plan metadata
					let datacenterId = checkpoint.approval_metadata?.datacenter_id;

					if (!datacenterId) {
						// Get from plan metadata
						const { data: plan, error: planError } = await supabase
							.from("recovery_plans_new")
							.select("execution_metadata")
							.eq("id", checkpoint.recovery_plan_id)
							.single();

						if (!planError && plan?.execution_metadata?.datacenter_id) {
							datacenterId = plan.execution_metadata.datacenter_id;
						}
					}

					if (!datacenterId) {
						datacenterId = "default";
						console.warn(`No datacenter ID found, using default: ${datacenterId}`);
					}

					// Resume execution using the new engine
					await executionEngine.resumeExecution(
						checkpoint.recovery_plan_id,
						checkpoint.id,
						datacenterId
					);

					console.log(
						`Successfully resumed execution using new engine for plan ${checkpoint.recovery_plan_id}`
					);
					return checkpoint;
				} catch (engineError: any) {
					console.error(`Failed to resume execution using new engine:`, engineError);
					throw new Error(`Failed to resume execution: ${engineError.message}`);
				}
			} else {
				console.log(`Approval decision is '${decision}', execution will remain paused`);
			}

			return { type: "checkpoint", checkpoint };
		}
	} catch (error: any) {
		console.error(`Error processing approval: ${error.message}`);
		throw error;
	}
};
