import { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "@/components/ui/sonner";
import * as apiClient from "@/lib/api/api-client";

export interface ExecutionStep {
	id: string;
	name: string;
	operation_type: string;
	step_order: number;
	status:
		| "pending"
		| "in_progress"
		| "completed"
		| "failed"
		| "awaiting_approval"
		| "approved"
		| "rejected";
	requires_approval: boolean;
	progress?: any[];
}

export interface ExecutionProgress {
	planId: string;
	executionId: string;
	status: "not_started" | "in_progress" | "paused" | "completed" | "failed";
	totalSteps: number;
	completedSteps: number;
	progressPercentage: number;
	steps: ExecutionStep[];
	checkpoints: any[];
	metadata: any;
}

/**
 * Enhanced hook for real-time execution progress tracking
 */
export const useExecutionProgress = (planId: string | undefined) => {
	const [progress, setProgress] = useState<ExecutionProgress | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isExecuting, setIsExecuting] = useState(false);
	const [lastEvent, setLastEvent] = useState<apiClient.RecoveryEvent | null>(null);

	const eventSourceRef = useRef<(() => void) | null>(null);
	const isMountedRef = useRef(true);

	/**
	 * Start execution of a recovery plan
	 */
	const startExecution = useCallback(
		async (datacenterId: string) => {
			if (!planId) {
				toast.error("Plan ID is required");
				return;
			}

			setIsExecuting(true);
			setError(null);

			try {
				await apiClient.apiRequest(`/api/gcp/recovery/v2/execute/${planId}/${datacenterId}`, {
					method: "POST",
				});

				toast.success("Recovery plan execution started", {
					description: "Real-time progress updates will appear below",
				});

				// Refresh progress data
				await fetchProgress();
			} catch (error: any) {
				console.error("Error starting execution:", error);
				setError(error.message);
				setIsExecuting(false);
				toast.error("Failed to start execution", {
					description: error.message,
				});
			}
		},
		[planId]
	);

	/**
	 * Resume execution from a checkpoint
	 */
	const resumeExecution = useCallback(
		async (checkpointId: string, datacenterId: string) => {
			if (!planId) {
				toast.error("Plan ID is required");
				return;
			}

			setIsExecuting(true);
			setError(null);

			try {
				await apiClient.apiRequest(`/api/gcp/recovery/v2/resume/${planId}`, {
					method: "POST",
					body: JSON.stringify({ checkpointId, datacenterId }),
				});

				toast.success("Recovery plan execution resumed", {
					description: "Continuing from the last checkpoint",
				});

				// Refresh progress data
				await fetchProgress();
			} catch (error: any) {
				console.error("Error resuming execution:", error);
				setError(error.message);
				setIsExecuting(false);
				toast.error("Failed to resume execution", {
					description: error.message,
				});
			}
		},
		[planId]
	);

	/**
	 * Cancel execution
	 */
	const cancelExecution = useCallback(async () => {
		if (!planId) {
			toast.error("Plan ID is required");
			return;
		}

		try {
			await apiClient.apiRequest(`/api/gcp/recovery/v2/cancel/${planId}`, {
				method: "POST",
			});

			toast.success("Execution cancelled successfully");
			setIsExecuting(false);

			// Refresh progress data
			await fetchProgress();
		} catch (error: any) {
			console.error("Error cancelling execution:", error);
			toast.error("Failed to cancel execution", {
				description: error.message,
			});
		}
	}, [planId]);

	/**
	 * Fetch current progress
	 */
	const fetchProgress = useCallback(async () => {
		if (!planId) return;

		setIsLoading(true);
		setError(null);

		try {
			const progressData = await apiClient.apiRequest<ExecutionProgress>(
				`/api/gcp/recovery/v2/progress/${planId}`
			);

			if (isMountedRef.current) {
				setProgress(progressData);
				setIsExecuting(progressData.status === "in_progress");
			}
		} catch (error: any) {
			console.error("Error fetching progress:", error);
			if (isMountedRef.current) {
				setError(error.message);
			}
		} finally {
			if (isMountedRef.current) {
				setIsLoading(false);
			}
		}
	}, [planId]);

	/**
	 * Handle SSE events
	 */
	const handleSSEEvent = useCallback((event: apiClient.RecoveryEvent) => {
		if (!isMountedRef.current) return;

		setLastEvent(event);

		// Update progress based on event type
		if (event.type === "plan_update") {
			setProgress((prev) =>
				prev
					? {
							...prev,
							status: event.status.toLowerCase() as any,
							metadata: { ...prev.metadata, ...event },
					  }
					: null
			);

			// Update execution state
			if (
				event.status === "EXECUTING" ||
				event.status === "RESUMING" ||
				event.status === "IN_PROGRESS"
			) {
				setIsExecuting(true);
			} else if (
				event.status === "COMPLETED" ||
				event.status === "FAILED" ||
				event.status === "PAUSED"
			) {
				setIsExecuting(false);
			}
		} else if (event.type === "step_update" && event.stepId) {
			setProgress((prev) => {
				if (!prev) return null;

				const updatedSteps = prev.steps.map((step) => {
					if (step.step_order.toString() === event.stepId) {
						return {
							...step,
							status: event.status.toLowerCase() as any,
						};
					}
					return step;
				});

				// Recalculate progress
				const completedSteps = updatedSteps.filter(
					(step) => step.status === "completed" || step.status === "approved"
				).length;
				const progressPercentage = Math.round((completedSteps / updatedSteps.length) * 100);

				return {
					...prev,
					steps: updatedSteps,
					completedSteps,
					progressPercentage,
				};
			});
		} else if (event.type === "connection") {
			// Handle connection events if needed
			console.log("SSE connection established for plan:", event.planId);
		}

		// Show toast notifications for important events
		if (event.type === "plan_update") {
			if (event.status === "COMPLETED") {
				toast.success("Recovery plan completed successfully!");
			} else if (event.status === "FAILED") {
				toast.error("Recovery plan execution failed", {
					description: event.error || "Unknown error occurred",
				});
			} else if (event.status === "PAUSED") {
				toast.info("Execution paused", {
					description: event.message || "Awaiting approval",
				});
			}
		}
	}, []);

	/**
	 * Setup SSE connection
	 */
	useEffect(() => {
		if (!planId) return;

		// Fetch initial progress
		fetchProgress();

		// Setup SSE connection
		const setupSSE = async () => {
			try {
				const cleanup = await apiClient.createRecoveryPlanEventSource(
					planId,
					handleSSEEvent,
					(error) => {
						console.error("SSE error:", error);
						setError("Connection lost. Retrying...");
					}
				);

				eventSourceRef.current = cleanup;
			} catch (error) {
				console.error("Failed to setup SSE:", error);
				setError("Failed to establish real-time connection");
			}
		};

		setupSSE();

		// Cleanup on unmount
		return () => {
			isMountedRef.current = false;
			if (eventSourceRef.current) {
				eventSourceRef.current();
			}
		};
	}, [planId, handleSSEEvent, fetchProgress]);

	return {
		progress,
		isLoading,
		error,
		isExecuting,
		lastEvent,
		startExecution,
		resumeExecution,
		cancelExecution,
		refreshProgress: fetchProgress,
	};
};
