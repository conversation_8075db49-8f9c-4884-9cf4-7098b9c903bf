export interface ExecutionContext {
	datacenterId: string;
	executionId: string;
	planId: string;
	[key: string]: any;
}

export interface ExecutionResult {
	success: boolean;
	message: string;
	data?: any;
	error?: string;
}

/**
 * Base class for all step executors
 * Provides common functionality and interface
 */
export abstract class BaseStepExecutor {
	protected stepType: string;

	constructor(stepType: string) {
		this.stepType = stepType;
	}

	/**
	 * Execute the step
	 * Must be implemented by concrete executors
	 */
	abstract execute(step: any, context: ExecutionContext): Promise<ExecutionResult>;

	/**
	 * Validate step configuration
	 * Can be overridden by concrete executors
	 */
	validate(step: any): boolean {
		if (!step) {
			throw new Error("Step is required");
		}
		if (!step.name) {
			throw new Error("Step name is required");
		}
		if (!step.operation_type) {
			throw new Error("Step operation type is required");
		}
		return true;
	}

	/**
	 * Get step type
	 */
	getStepType(): string {
		return this.stepType;
	}

	/**
	 * Log step execution
	 */
	protected log(message: string, level: "info" | "warn" | "error" = "info"): void {
		const timestamp = new Date().toISOString();
		console.log(
			`[${timestamp}] [${this.stepType.toUpperCase()}] ${level.toUpperCase()}: ${message}`
		);
	}

	/**
	 * Create success result
	 */
	protected createSuccessResult(message: string, data?: any): ExecutionResult {
		return {
			success: true,
			message,
			data,
		};
	}

	/**
	 * Create error result
	 */
	protected createErrorResult(message: string, error?: string): ExecutionResult {
		return {
			success: false,
			message,
			error,
		};
	}

	/**
	 * Sleep for specified milliseconds
	 */
	protected sleep(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Parse step configuration
	 */
	protected parseConfiguration(step: any): any {
		try {
			if (typeof step.configuration === "string") {
				return JSON.parse(step.configuration);
			}
			return step.configuration || {};
		} catch (error: any) {
			this.log(`Error parsing step configuration: ${error.message}`, "error");
			return {};
		}
	}

	/**
	 * Get configuration value with default
	 */
	protected getConfigValue(config: any, key: string, defaultValue?: any): any {
		return config[key] !== undefined ? config[key] : defaultValue;
	}

	/**
	 * Validate required configuration keys
	 */
	protected validateRequiredConfig(config: any, requiredKeys: string[]): void {
		const missingKeys = requiredKeys.filter((key) => config[key] === undefined);
		if (missingKeys.length > 0) {
			throw new Error(`Missing required configuration keys: ${missingKeys.join(", ")}`);
		}
	}
}
