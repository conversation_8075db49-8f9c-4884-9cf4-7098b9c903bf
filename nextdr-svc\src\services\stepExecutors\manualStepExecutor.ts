import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

/**
 * Executor for manual steps
 * These steps require human intervention and are marked as completed when manually confirmed
 */
export class ManualStepExecutor extends BaseStepExecutor {
	constructor() {
		super("Manual step");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Processing manual step: ${step.name}`);

		try {
			this.validate(step);
			const config = this.parseConfiguration(step);

			const { instructions, estimated_duration, verification_required, assignee } = config;

			// Manual steps are typically marked as completed externally
			// This executor just logs the step and provides instructions

			this.log(`Manual step instructions: ${instructions || "No specific instructions provided"}`);

			if (assignee) {
				this.log(`Assigned to: ${assignee}`);
			}

			if (estimated_duration) {
				this.log(`Estimated duration: ${estimated_duration}`);
			}

			// For manual steps, we typically return success immediately
			// The actual completion is tracked separately through the UI or API
			return this.createSuccessResult("Manual step ready for execution", {
				instructions,
				estimated_duration,
				verification_required,
				assignee,
				status: "awaiting_manual_completion",
				created_at: new Date().toISOString(),
			});
		} catch (error: any) {
			this.log(`Manual step processing failed: ${error.message}`, "error");
			return this.createErrorResult(
				`Manual step processing failed: ${error.message}`,
				error.message
			);
		}
	}

	validate(step: any): boolean {
		super.validate(step);

		if (step.operation_type !== "Manual step") {
			throw new Error(`Invalid operation type for manual step: ${step.operation_type}`);
		}

		return true;
	}
}
